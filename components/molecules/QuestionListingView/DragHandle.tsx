'use client';

import React from 'react';
import { cn } from '@/utils/cn';
import Icon from '@/components/atoms/Icon';

export interface DragHandleProps {
  className?: string;
  disabled?: boolean;
  'aria-label'?: string;
  listeners?: any; // DndKit listeners
  attributes?: any; // DndKit attributes
}

/**
 * DragHandle component for drag-and-drop functionality
 * Displays a 6-dot grid icon that serves as the drag handle
 * Includes proper accessibility and mobile-friendly touch support
 */
export const DragHandle: React.FC<DragHandleProps> = ({
  className = '',
  disabled = false,
  'aria-label': ariaLabel = 'Drag to reorder',
  listeners,
  attributes,
}) => {
  return (
    <button
      type="button"
      className={cn(
        // Base styles
        'flex items-center justify-center',
        'w-8 h-8 rounded-md',
        'transition-all duration-200 ease-in-out',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',

        // Interactive states
        disabled
          ? 'cursor-not-allowed opacity-40'
          : 'cursor-grab active:cursor-grabbing hover:bg-gray-100',

        // Mobile-friendly touch target (44px minimum for accessibility)
        'touch-manipulation',
        'min-w-[44px] min-h-[44px] md:min-w-[32px] md:min-h-[32px]',
        // Better touch feedback on mobile
        'active:scale-95 active:bg-gray-200',

        className
      )}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby="drag-instructions"
      aria-roledescription="sortable"
      tabIndex={disabled ? -1 : 0}
      {...listeners}
      {...attributes}
    >
      <Icon
        variant="grip-vertical"
        size={16}
        className={cn(
          'text-gray-400 transition-colors duration-200',
          !disabled && 'group-hover:text-gray-600'
        )}
        aria-hidden="true"
      />

      {/* Screen reader instructions */}
      <span id="drag-instructions" className="sr-only">
        Use arrow keys to reorder, or drag with mouse or touch. Press Enter or Space to activate drag mode.
      </span>
    </button>
  );
};

export default DragHandle;

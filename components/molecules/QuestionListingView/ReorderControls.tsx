'use client';

import React from 'react';
import { cn } from '@/utils/cn';
import Icon from '@/components/atoms/Icon';
import { Button } from '@/components/atoms/Button/Button';

export interface ReorderControlsProps {
  className?: string;
  isVisible?: boolean;
  canMoveUp?: boolean;
  canMoveDown?: boolean;
  hasChanges?: boolean;
  isUpdating?: boolean;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
  onUpdateOrder?: () => void;
}

/**
 * ReorderControls component for manual question reordering
 * Shows up/down arrows and Update Order button on hover
 * Includes proper animations and loading states
 */
export const ReorderControls: React.FC<ReorderControlsProps> = ({
  className = '',
  isVisible = false,
  canMoveUp = true,
  canMoveDown = true,
  hasChanges = false,
  isUpdating = false,
  onMoveUp,
  onMoveDown,
  onUpdateOrder,
}) => {
  return (
    <div
      className={cn(
        // Base layout
        'flex items-center gap-2',
        'transition-all duration-300 ease-in-out',
        
        // Visibility animation
        isVisible
          ? 'opacity-100 translate-x-0 pointer-events-auto'
          : 'opacity-0 translate-x-2 pointer-events-none',
        
        className
      )}
    >
      {/* Move Up Button */}
      <button
        type="button"
        onClick={onMoveUp}
        disabled={!canMoveUp || isUpdating}
        className={cn(
          'flex items-center justify-center',
          'w-8 h-8 rounded-md',
          'transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          // Mobile-friendly touch targets
          'min-w-[44px] min-h-[44px] md:min-w-[32px] md:min-h-[32px]',
          'touch-manipulation',

          canMoveUp && !isUpdating
            ? 'hover:bg-gray-100 active:bg-gray-200 text-gray-600 hover:text-gray-800'
            : 'text-gray-300 cursor-not-allowed'
        )}
        aria-label={canMoveUp ? "Move question up one position" : "Cannot move question up (already at top)"}
        title={canMoveUp ? "Move up" : "Cannot move up"}
      >
        <Icon variant="arrow-up" size={14} aria-hidden="true" />
      </button>

      {/* Move Down Button */}
      <button
        type="button"
        onClick={onMoveDown}
        disabled={!canMoveDown || isUpdating}
        className={cn(
          'flex items-center justify-center',
          'w-8 h-8 rounded-md',
          'transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          // Mobile-friendly touch targets
          'min-w-[44px] min-h-[44px] md:min-w-[32px] md:min-h-[32px]',
          'touch-manipulation',

          canMoveDown && !isUpdating
            ? 'hover:bg-gray-100 active:bg-gray-200 text-gray-600 hover:text-gray-800'
            : 'text-gray-300 cursor-not-allowed'
        )}
        aria-label={canMoveDown ? "Move question down one position" : "Cannot move question down (already at bottom)"}
        title={canMoveDown ? "Move down" : "Cannot move down"}
      >
        <Icon variant="arrow-down" size={14} aria-hidden="true" />
      </button>

      {/* Update Order Button */}
      {hasChanges && (
        <Button
          onClick={onUpdateOrder}
          disabled={isUpdating}
          isLoading={isUpdating}
          className={cn(
            'ml-2 px-3 py-1.5 text-xs',
            'bg-blue-600 hover:bg-blue-700 text-white',
            'transition-all duration-200',
            'animate-in slide-in-from-right-2'
          )}
        >
          {isUpdating ? 'Updating...' : 'Update Order'}
        </Button>
      )}
    </div>
  );
};

export default ReorderControls;

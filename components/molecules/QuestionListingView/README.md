# QuestionListingView with Drag-and-Drop Reordering

Enhanced QuestionListingView component with drag-and-drop question reordering functionality.

## Features

✅ **Drag-and-Drop Reordering**: Smooth drag-and-drop using @dnd-kit libraries
✅ **Manual Reordering**: Up/down arrow buttons for precise control
✅ **Visual Feedback**: Hover effects, drag overlays, and loading states
✅ **Server Integration**: Automatic server sync with rollback on failure
✅ **Mobile Optimized**: Touch-friendly interactions and responsive design
✅ **Accessibility**: Full keyboard navigation and screen reader support
✅ **Optimistic Updates**: Immediate UI feedback with error handling

## Installation

The required dependencies are already installed:
- `@dnd-kit/core`
- `@dnd-kit/sortable` 
- `@dnd-kit/utilities`

## Basic Usage

```tsx
import QuestionListingView, { Question } from './QuestionListingView';

const questions: Question[] = [
  {
    questionId: 'q1', // Required for server updates
    type: 'multiple_choice',
    content: 'What is the capital of France?',
    options: ['London', 'Berlin', 'Paris', 'Madrid'],
    answer: ['Paris'],
    explain: 'Paris is the capital of France.',
    subject: 'Geography'
  },
  // ... more questions
];

function MyComponent() {
  const [questions, setQuestions] = useState(initialQuestions);

  return (
    <QuestionListingView
      questions={questions}
      worksheetId="worksheet-123" // Required for server updates
      onOrderChange={setQuestions} // Handle local order changes
      readOnly={false} // Enable reordering
      isHtmlContent={false}
      worksheetInfo={{
        topic: 'Geography Quiz',
        subject: 'Geography',
        grade: 'Grade 8',
        totalQuestions: questions.length
      }}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `questions` | `Question[]` | `[]` | Array of questions to display |
| `worksheetId` | `string?` | - | Required for server updates |
| `onOrderChange` | `(newOrder: Question[]) => void` | - | Callback when order changes |
| `readOnly` | `boolean` | `false` | Disable reordering functionality |
| `isHtmlContent` | `boolean` | `false` | Render content as HTML |
| `containerClass` | `string` | `''` | Additional CSS classes |
| `worksheetInfo` | `object` | - | Worksheet metadata |

## Question Type

```tsx
type Question = {
  questionId?: string; // Required for server updates
  type: 'multiple_choice' | 'single_choice' | 'fill_blank' | 'creative_writing' | string;
  content: string;
  image?: string | null;
  svgCode?: string;
  imagePrompt?: string | null;
  options: string[];
  answer: string[];
  explain: string;
  prompt?: string; // For creative writing
  subject?: string; // Question subject
};
```

## Server Integration

The component uses `reorderWorksheetQuestionsAction` for server updates:

```tsx
// Server action expects this format:
{
  reorders: Array<{
    questionId: string,
    newPosition: number
  }>
}
```

## Behavior

### Without `worksheetId`
- Drag-and-drop works locally
- Shows success message for local changes
- No server updates

### With `worksheetId` but no `questionId`
- Drag-and-drop works locally
- Shows error when trying to save to server
- Explains that `questionId` is required

### With both `worksheetId` and `questionId`
- Full functionality enabled
- Automatic server sync
- Error handling with rollback

## Accessibility

- **Keyboard Navigation**: Arrow keys for reordering
- **Screen Reader**: Live announcements for changes
- **ARIA Labels**: Proper labeling for all interactive elements
- **Focus Management**: Clear focus indicators

## Mobile Support

- **Touch Targets**: 44px minimum touch targets
- **Touch Feedback**: Visual feedback on touch
- **Responsive Design**: Adapts to mobile screens
- **Always Visible Controls**: Controls visible on mobile

## Testing

Use the test component to verify functionality:

```tsx
import QuestionListingViewTest from './QuestionListingViewTest';

// Renders a complete test interface with sample data
<QuestionListingViewTest />
```

## Error Handling

The component handles various error scenarios:
- Network failures during server sync
- Missing `questionId` for server updates
- Invalid server responses
- Automatic rollback on errors

## Performance

- **Optimistic Updates**: Immediate UI feedback
- **Debounced Server Calls**: Prevents excessive API calls
- **Memoized Calculations**: Efficient re-renders
- **Lazy Loading**: Components load as needed

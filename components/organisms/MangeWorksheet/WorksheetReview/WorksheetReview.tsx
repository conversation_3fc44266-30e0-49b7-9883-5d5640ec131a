import {
  getWorksheetDetail,
  WorksheetGeneratingStatus,
} from '@/apis/worksheet';
import { WorksheetProgressView } from '@/components/molecules/WorksheetProgressView/WorksheetProgressView';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { onSeverSession } from '@/config/auth';
import { getFileRenderUrl } from '@/utils/fileUtils';
import { transformQuestionsForComponent } from '@/utils/questionUtils';

export const WorksheetReview: React.FC<{ id?: string }> = async ({ id }) => {
  if (!id) return <></>;
  const response = await getWorksheetDetail(id);
  if (response.status === 'error') return <div>Something wrong!</div>;

  // Get school information from session
  const ssrSession = await onSeverSession();
  let schoolInfo: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  } | undefined = undefined; // Initialize with undefined and provide type
  if (ssrSession?.user?.school) {
    // Get the logo file ID and convert it to a proper render URL
    const logoFileId = ssrSession.user.school.brand?.logo || ssrSession.user.school.brand?.image;
    const logoUrl = logoFileId ? getFileRenderUrl(logoFileId) : undefined;

    schoolInfo = {
      name: ssrSession.user.school.name,
      address: ssrSession.user.school.address,
      phoneNumber: ssrSession.user.school.phoneNumber,
      registeredNumber: ssrSession.user.school.registeredNumber,
      email: ssrSession.user.school.email,
      logoUrl: logoUrl
    };
  }

  // Extract questions from the API response
  const rawQuestions = response.data?.promptResult?.result || [];
  const initialQuestions = transformQuestionsForComponent(rawQuestions);

  // Debug API response structure
  console.log('🔍 WorksheetReview API response:', {
    responseStatus: response.status,
    hasData: !!response.data,
    hasPromptResult: !!response.data?.promptResult,
    hasResult: !!response.data?.promptResult?.result,
    resultLength: rawQuestions.length,
    rawQuestionsData: rawQuestions,
    transformedQuestionsLength: initialQuestions.length,
    transformedQuestionsData: initialQuestions,
    firstRawQuestion: rawQuestions[0],
    firstTransformedQuestion: initialQuestions[0],
    generatingStatus: response.data?.generatingStatus
  });

  // Calculate initial progress based on the number of questions
  // This is a simple estimation since we don't have actual progress data from the API
  const initialProgress: ProgressData = initialQuestions.length > 0 ? {
    current: initialQuestions.length,
    total: Math.max(10, initialQuestions.length), // Assume at least 10 questions total
    percentage: Math.min(100, Math.round((initialQuestions.length / 10) * 100))
  } : {
    current: 0,
    total: 10,
    percentage: 0
  };

  // Extract worksheet information from the response
  const worksheetInfo = {
    topic: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'topic')?.optionValue?.label,
    subject: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'subject')?.optionValue?.label, // Added subject
    grade: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'grade')?.optionValue?.label,
    language: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'language')?.optionValue?.label,
    level: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'level')?.optionValue?.label,
    totalQuestions: initialQuestions.length || initialProgress.total,
  };

  return (
    <WorksheetProgressView
      worksheetId={id}
      initialStatus={response.data?.generatingStatus || WorksheetGeneratingStatus.PENDING}
      initialQuestions={initialQuestions}
      initialProgress={initialProgress}
      worksheetInfo={worksheetInfo}
      schoolInfo={schoolInfo}
    />
  );
};

import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { TRHFQuestionValue } from '@/components/molecules/FormItems/RHFQuestion/RHFQuestion';
import { DEFAULT_QUESTION_COUNT, STANDARD_QUESTION_COUNTS } from './constants';

/**
 * Accumulates new questions with existing ones, avoiding duplicates based on content
 * @param prevQuestions - Existing questions array
 * @param newQuestions - New questions to add
 * @returns Updated array with all unique questions
 */
export const accumulateQuestions = (prevQuestions: Question[], newQuestions: Question[]): Question[] => {
  console.log('🔍 accumulateQuestions called:', {
    prevQuestionsLength: prevQuestions.length,
    newQuestionsLength: newQuestions.length,
    prevQuestions: prevQuestions,
    newQuestions: newQuestions
  });

  // Transform new questions to ensure proper structure
  const transformedNewQuestions = transformQuestionsForComponent(newQuestions);

  // Create a map of existing questions to avoid duplicates
  // Using content as a unique identifier
  const existingQuestionsMap = new Map(
    prevQuestions.map(q => [q.content, q])
  );

  // Add new questions that don't already exist
  transformedNewQuestions.forEach(newQuestion => {
    if (!existingQuestionsMap.has(newQuestion.content)) {
      existingQuestionsMap.set(newQuestion.content, newQuestion);
    }
  });

  // Convert map back to array
  const result = Array.from(existingQuestionsMap.values());

  console.log('🔍 accumulateQuestions result:', {
    resultLength: result.length,
    result: result
  });

  return result;
};

/**
 * Transforms API question data to ensure compatibility with QuestionListingView
 * Maps 'id' field to 'questionId' if needed
 * @param questions - Questions from API response
 * @returns Transformed questions with proper field mapping
 */
export const transformQuestionsForComponent = (questions: any[]): Question[] => {
  if (!Array.isArray(questions)) {
    console.warn('🔍 transformQuestionsForComponent: Input is not an array:', questions);
    return [];
  }

  return questions.map((q, index) => {
    if (!q || typeof q !== 'object') {
      console.warn(`🔍 transformQuestionsForComponent: Question at index ${index} is not a valid object:`, q);
      return null;
    }

    // Ensure we have either questionId or id
    const questionId = q.questionId || q.id;

    const transformed = {
      ...q,
      questionId: questionId, // Ensure questionId is set
      id: q.id || questionId, // Keep id for backward compatibility
      // Ensure required fields have defaults
      content: q.content || '',
      options: Array.isArray(q.options) ? q.options : [],
      answer: Array.isArray(q.answer) ? q.answer : [],
      explain: q.explain || '',
      type: q.type || 'multiple_choice'
    };

    console.log(`🔍 transformQuestionsForComponent: Transformed question ${index}:`, {
      original: q,
      transformed: transformed,
      hasQuestionId: !!transformed.questionId,
      hasId: !!transformed.id
    });

    return transformed;
  }).filter(Boolean) as Question[]; // Remove any null entries
};

/**
 * Parses the question count value from various input types.
 *
 * @param value - The value to parse. Can be TRHFQuestionValue, string, or undefined.
 * @param defaultValue - The default value to return if parsing fails or input is undefined.
 * @returns The parsed question count as a number.
 */
export const parseQuestionCount = (
  value: TRHFQuestionValue | string | undefined,
  defaultValue: number = DEFAULT_QUESTION_COUNT
): number => {
  if (value === undefined || value === null) {
    console.log('[parseQuestionCount] Value is undefined or null, returning default:', defaultValue);
    return defaultValue;
  }

  if (typeof value === 'string') {
    const parsedString = parseInt(value, 10);
    if (!isNaN(parsedString)) {
      return parsedString;
    }
    // Try to extract number from string if it's like "X Questions"
    const match = value.match(/\d+/);
    if (match && match[0]) {
      const parsedFromLabel = parseInt(match[0], 10);
      if (!isNaN(parsedFromLabel)) {
        return parsedFromLabel;
      }
    }
    console.warn('[parseQuestionCount] String value could not be parsed to a number:', value);
    return defaultValue;
  }

  // TRHFQuestionValue object
  if (typeof value === 'object' && value !== null) {
    if (value.isCustom) {
      if (value.customValue !== undefined && value.customValue !== null && String(value.customValue).trim() !== '') {
        const parsedCustom = parseInt(String(value.customValue), 10);
        if (!isNaN(parsedCustom)) {
          return parsedCustom;
        }
        console.warn('[parseQuestionCount] Custom value is not a valid number:', value.customValue);
      } else {
        console.log('[parseQuestionCount] Custom value is empty or undefined, returning default for custom.');
      }
      return defaultValue; // Default if custom value is invalid or empty
    } else {
      // Predefined option
      if (value.value !== undefined) {
        const parsedValue = parseInt(String(value.value), 10);
        if (!isNaN(parsedValue) && STANDARD_QUESTION_COUNTS.includes(parsedValue)) {
          return parsedValue;
        }
      }
      // Fallback to label if value is not a standard one or not parsable
      if (value.label !== undefined) {
        const labelStr = String(value.label);
        // Check if the label itself is one of the standard numbers
        const standardLabelMatch = STANDARD_QUESTION_COUNTS.find(stdCount => String(stdCount) === labelStr);
        if (standardLabelMatch !== undefined) {
          return standardLabelMatch;
        }
        // Try to extract number from label string e.g., "20 Questions"
        const match = labelStr.match(/\d+/);
        if (match && match[0]) {
          const parsedFromLabel = parseInt(match[0], 10);
          if (!isNaN(parsedFromLabel)) {
            return parsedFromLabel;
          }
        }
      }
      console.warn('[parseQuestionCount] Predefined TRHFQuestionValue could not be reliably parsed:', value);
      return defaultValue;
    }
  }

  console.error('[parseQuestionCount] Unexpected value type or structure:', value);
  return defaultValue;
};
